<!DOCTYPE html>
<html lang="en">
<head>
    {% load static %}
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}EEU Employee Attendance System{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{% static 'imgs/eeu_logo.png' %}"

    <!-- Local Tailwind CSS -->
    <link href="{% static 'dist/styles.css' %}" rel="stylesheet">

    <!-- Font Awesome - TODO: Replace with local version -->
    <!-- For now using CDN, see instructions below for local installation -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom Styles -->
    <style type="text/css">
        /* EEU Emerald theme colors */
        :root {
            --eeu-sidebar: #111827;
            --eeu-primary: #10b981;        /* Emerald 500 */
            --eeu-primary-dark: #059669;   /* Emerald 600 - for hover states */
            --eeu-primary-light: #34d399;  /* Emerald 400 - for lighter accents */
            --eeu-primary-bg: #d1fae5;     /* Emerald 100 - for backgrounds */
            --eeu-active: rgba(255, 255, 255, 0.08);
            --eeu-hover: rgba(255, 255, 255, 0.08);
            --sidebar-width: 80px;
        }

        /* Sidebar active state styling */
        .sidebar-item.active {
            background-color: var(--eeu-active);
        }
        .sidebar-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background-color: var(--eeu-primary);
            border-radius: 0 3px 3px 0;
        }

        /* Custom utility classes for EEU emerald theme */
        .bg-eeu-sidebar { background-color: var(--eeu-sidebar); }
        .bg-eeu-primary { background-color: var(--eeu-primary); }
        .bg-eeu-primary-dark { background-color: var(--eeu-primary-dark); }
        .bg-eeu-primary-light { background-color: var(--eeu-primary-light); }
        .bg-eeu-primary-bg { background-color: var(--eeu-primary-bg); }
        .bg-eeu-active { background-color: var(--eeu-active); }
        .bg-eeu-hover { background-color: var(--eeu-hover); }
        .text-eeu-primary { color: var(--eeu-primary); }
        .text-eeu-primary-dark { color: var(--eeu-primary-dark); }
        .border-eeu-primary { border-color: var(--eeu-primary); }
        .hover\:bg-eeu-hover:hover { background-color: var(--eeu-hover); }
        .hover\:bg-eeu-primary:hover { background-color: var(--eeu-primary); }
        .hover\:bg-eeu-primary-dark:hover { background-color: var(--eeu-primary-dark); }
        .hover\:text-eeu-primary:hover { color: var(--eeu-primary); }
        .focus\:border-eeu-primary:focus { border-color: var(--eeu-primary); }
        .focus\:ring-eeu-primary:focus { --tw-ring-color: var(--eeu-primary); }

        /* Sidebar width utilities */
        .w-sidebar { width: var(--sidebar-width); }
        .pl-sidebar { padding-left: var(--sidebar-width); }
        .left-sidebar { left: var(--sidebar-width); }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body class="font-sans bg-gray-50 text-gray-800 pl-sidebar pt-14">
    <!-- Sidebar -->
    <div class="fixed top-0 left-0 bottom-0 w-sidebar bg-eeu-sidebar z-50 overflow-y-auto">
        <!-- Logo Section -->
        <div class="flex items-center justify-center py-4 border-b border-gray-700">
            <img src="{% static 'imgs/eeu_logo_w.png' %}" alt="EEU Logo" class="w-10 h-10 object-contain">
        </div>
        <!-- Dashboard -->
        <a href="{% url 'dashboard' %}" class="sidebar-item {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %} flex flex-col items-center py-2 {% if request.resolver_match.url_name == 'dashboard' %}text-white{% else %}text-gray-400{% endif %} cursor-pointer relative hover:bg-eeu-hover hover:text-white no-underline">
            <i class="fa-solid fa-chart-pie text-xl mb-1"></i>
            <span class="text-xs font-medium">Dashboard</span>
        </a>

        <!-- Attendance -->
        <a href="{% url 'attendance' %}" class="sidebar-item {% if request.resolver_match.url_name == 'attendance' %}active{% endif %} flex flex-col items-center py-2 {% if request.resolver_match.url_name == 'attendance' %}text-white{% else %}text-gray-400{% endif %} cursor-pointer relative hover:bg-eeu-hover hover:text-white no-underline">
            <i class="fas fa-user-check text-xl mb-1"></i>
            <span class="text-xs font-medium">Attendance</span>
        </a>

        <!-- Employees -->
        <a href="{% url 'contacts' %}" class="sidebar-item {% if request.resolver_match.url_name == 'contacts' %}active{% endif %} flex flex-col items-center py-2 {% if request.resolver_match.url_name == 'contacts' %}text-white{% else %}text-gray-400{% endif %} cursor-pointer relative hover:bg-eeu-hover hover:text-white no-underline">
            <i class="fas fa-user-friends text-xl mb-1"></i>
            <span class="text-xs font-medium">Employees</span>
        </a>

        <!-- Devices (replacing Calls) -->
        <a href="{% url 'device' %}" class="sidebar-item {% if request.resolver_match.url_name == 'device' %}active{% endif %} flex flex-col items-center py-2 {% if request.resolver_match.url_name == 'device' %}text-white{% else %}text-gray-400{% endif %} cursor-pointer relative hover:bg-eeu-hover hover:text-white no-underline">
            <i class="fas fa-mobile-alt text-xl mb-1"></i>
            <span class="text-xs font-medium">Devices</span>
        </a>

        <!-- Settings -->
        <a href="{% url 'settings' %}" class="sidebar-item {% if request.resolver_match.url_name == 'settings' %}active{% endif %} flex flex-col items-center py-2 {% if request.resolver_match.url_name == 'settings' %}text-white{% else %}text-gray-400{% endif %} cursor-pointer relative hover:bg-eeu-hover hover:text-white no-underline">
            <i class="fas fa-cog text-xl mb-1"></i>
            <span class="text-xs font-medium">Settings</span>
        </a>
    </div>

    <!-- Top Bar -->
    <div class="fixed top-0 left-sidebar right-0 h-14 bg-white border-b border-gray-100 z-40 flex items-center justify-between px-6 shadow-sm">
        <h4 class="text-lg font-semibold">{% block page_title %}EEU Employee Attendance{% endblock %}</h4>

        <div class="flex items-center space-x-4">
            <!-- Notifications -->
            <div class="relative">
                <i class="fas fa-bell text-gray-500 text-xl hover:text-eeu-primary transition-colors"></i>
                <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center font-semibold">3</span>
            </div>

            <!-- User Dropdown -->
            <div class="relative flex items-center space-x-2 group">
                <div class="flex items-center cursor-pointer" id="userDropdown">
                    <div class="w-9 h-9 rounded-full bg-gradient-to-br from-eeu-primary to-eeu-primary-light text-white flex items-center justify-center font-semibold">
                        JD
                    </div>
                    <div class="ml-2 hidden md:block">
                        <div class="text-sm font-medium text-gray-800">John Doe</div>
                    </div>
                    <i class="fas fa-chevron-down ml-1 text-gray-500 text-xs group-hover:text-eeu-primary transition-colors"></i>
                </div>

                <ul class="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg py-1 z-50 hidden" aria-labelledby="userDropdown">
                    <li><h6 class="px-4 py-2 text-sm font-semibold text-gray-800">John Doe</h6></li>
                    <li><hr class="my-1 border-gray-100"></li>
                    <li><a href="{% url 'profile' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-eeu-primary-bg hover:text-eeu-primary-dark transition-colors"><i class="fas fa-user mr-2"></i>Profile</a></li>
                    <li><a href="{% url 'change_password' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-eeu-primary-bg hover:text-eeu-primary-dark transition-colors"><i class="fas fa-key mr-2"></i>Change Password</a></li>
                    <li><hr class="my-1 border-gray-100"></li>
                    <li><a href="{% url 'logout' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-eeu-primary-bg hover:text-eeu-primary-dark transition-colors"><i class="fas fa-sign-out-alt mr-2"></i>Logout</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Content Area -->
    <div class="p-6">
        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
            <div class="p-6">
                {% block content %}
                <h2 class="text-2xl font-semibold mb-4">{% block content_title %}EEU Employee Attendance System{% endblock %}</h2>
                <p class="text-gray-600 leading-relaxed">
                    {% block content_text %}
                        Welcome to the EEU Employee Attendance System. Monitor employee attendance, manage records, and track performance with our modern, user-friendly interface.
                    {% endblock %}
                </p>
                {% endblock %}
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Simple dropdown toggle
        document.getElementById('userDropdown').addEventListener('click', function(e) {
            e.stopPropagation();
            const dropdown = this.closest('.relative').querySelector('ul');
            dropdown.classList.toggle('hidden');
        });

        // Close dropdown when clicking elsewhere
        document.addEventListener('click', function() {
            const dropdowns = document.querySelectorAll('[aria-labelledby="userDropdown"]');
            dropdowns.forEach(dropdown => {
                dropdown.classList.add('hidden');
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>