<!DOCTYPE html>
<html lang="en">
<head>
    {% load static %}
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - EEU Employee Attendance System</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{% static 'imgs/eeu_logo.png' %}"
    
    <!-- Local Tailwind CSS -->
    <link href="{% static 'dist/styles.css' %}" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom Styles -->
    <style type="text/css">
        /* EEU Emerald theme colors */
        :root {
            --eeu-sidebar: #111827;
            --eeu-primary: #10b981;        /* Emerald 500 */
            --eeu-primary-dark: #059669;   /* Emerald 600 - for hover states */
            --eeu-primary-light: #34d399;  /* Emerald 400 - for lighter accents */
            --eeu-primary-bg: #d1fae5;     /* Emerald 100 - for backgrounds */
        }

        .bg-eeu-sidebar { background-color: var(--eeu-sidebar); }
        .bg-eeu-primary { background-color: var(--eeu-primary); }
        .bg-eeu-primary-dark { background-color: var(--eeu-primary-dark); }
        .text-eeu-primary { color: var(--eeu-primary); }
        .hover\:bg-eeu-primary:hover { background-color: var(--eeu-primary); }
        .hover\:bg-eeu-primary-dark:hover { background-color: var(--eeu-primary-dark); }
        .border-eeu-primary { border-color: var(--eeu-primary); }
        .focus\:border-eeu-primary:focus { border-color: var(--eeu-primary); }
        .focus\:ring-eeu-primary:focus { --tw-ring-color: var(--eeu-primary); }
        
        /* Login form animations */
        .login-container {
            animation: fadeInUp 0.6s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Input focus effects */
        .input-group {
            position: relative;
        }
        
        .input-group input:focus + .input-icon {
            color: var(--telegram-primary);
        }
        
        /* Background pattern */
        .bg-pattern {
            background-image: 
                radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
        }
    </style>
</head>
<body class="min-h-screen bg-gray-50 bg-pattern flex items-center justify-center p-4">
    <!-- Login Container -->
    <div class="login-container w-full max-w-md">
        <!-- Logo/Brand Section -->
        <div class="text-center mb-8">
            <div class="w-20 h-20 mx-auto mb-4">
                <img src="{% static 'imgs/eeu_logo.png' %}" alt="EEU Logo" class="w-full h-full object-contain">
            </div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Employee Attendance System</h1>
            <p class="text-gray-600">Sign in to access the system</p>
        </div>
        
        <!-- Login Form -->
        <div class="bg-white rounded-2xl shadow-xl p-8">
            <!-- Error Messages -->
            {% if form.errors %}
                <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div class="flex items-start">
                        <i class="fas fa-exclamation-circle text-red-500 mt-0.5 mr-3"></i>
                        <div>
                            <h4 class="text-sm font-medium text-red-800 mb-1">Login Failed</h4>
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}
                                    <p class="text-sm text-red-700">{{ error }}</p>
                                {% endfor %}
                            {% endfor %}
                        </div>
                    </div>
                </div>
            {% endif %}
            
            <!-- Success Messages -->
            {% if messages %}
                {% for message in messages %}
                    <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-0.5 mr-3"></i>
                            <p class="text-sm text-green-700">{{ message }}</p>
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
            
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <!-- Username Field -->
                <div class="input-group">
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                        Username or Email
                    </label>
                    <div class="relative">
                        <input type="text" 
                               id="username" 
                               name="username" 
                               required
                               class="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-telegram-primary focus:border-telegram-primary transition-colors text-sm"
                               placeholder="Enter your username or email"
                               value="{{ form.username.value|default:'' }}">
                        <div class="input-icon absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 transition-colors">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>
                </div>
                
                <!-- Password Field -->
                <div class="input-group">
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        Password
                    </label>
                    <div class="relative">
                        <input type="password" 
                               id="password" 
                               name="password" 
                               required
                               class="w-full pl-12 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-telegram-primary focus:border-telegram-primary transition-colors text-sm"
                               placeholder="Enter your password">
                        <div class="input-icon absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 transition-colors">
                            <i class="fas fa-lock"></i>
                        </div>
                        <button type="button" 
                                class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                                onclick="togglePassword()">
                            <i class="fas fa-eye" id="password-toggle-icon"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Remember Me & Forgot Password -->
                <div class="flex items-center justify-between">
                    <label class="flex items-center">
                        <input type="checkbox" name="remember_me" class="w-4 h-4 text-telegram-primary border-gray-300 rounded focus:ring-telegram-primary">
                        <span class="ml-2 text-sm text-gray-600">Remember me</span>
                    </label>
                    <a href="#" class="text-sm text-telegram-primary hover:underline">
                        Forgot password?
                    </a>
                </div>
                
                <!-- Login Button -->
                <button type="submit" 
                        class="w-full bg-telegram-primary hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Sign In
                </button>
            </form>
            

        </div>
        
        <!-- Footer Info -->
        <div class="text-center mt-8">
            <p class="text-xs text-gray-500">
                © 2025 EEU Employee Attendance System. All rights reserved.
            </p>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const toggleIcon = document.getElementById('password-toggle-icon');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
        
        // Auto-focus on username field
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });
        
        // Form validation feedback
        const form = document.querySelector('form');
        const inputs = form.querySelectorAll('input[required]');
        
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (this.value.trim() === '') {
                    this.classList.add('border-red-300');
                    this.classList.remove('border-gray-300');
                } else {
                    this.classList.remove('border-red-300');
                    this.classList.add('border-gray-300');
                }
            });
            
            input.addEventListener('input', function() {
                if (this.value.trim() !== '') {
                    this.classList.remove('border-red-300');
                    this.classList.add('border-gray-300');
                }
            });
        });
    </script>
</body>
</html>
